package com.ruoyi.framework.mybatis;

import com.ruoyi.common.annotation.TableId;
import com.ruoyi.common.annotation.TableName;
import com.ruoyi.common.context.TenantContext;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.id.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

@Intercepts({@Signature(
        type = Executor.class,
        method = "update",
        args = {MappedStatement.class, Object.class}
)})
@Component
public class IdGeneratorInterceptor implements Interceptor {
    @Autowired
    private IdGenerator idGenerator;

    private static final String[] IGNORE_TABLES = {"sys_shop","sys_tenant", "gen_table", "gen_table_column", "information_schema.tables", "information_schema.columns", "wendao_shop_info", "wendao_sms_log", "wendao_sys_login_log", "wendao_sys_user", "wendao_wx_qrcode_record"};

    /**
     * 应该只拦截insert
     *
     * @param invocation
     * @return
     * @throws Throwable
     */
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取 MappedStatement 对象
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];

        // 获取 SQL 命令类型
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();

        // 判断是否是 INSERT 语句
        if (sqlCommandType == SqlCommandType.INSERT) {
            Object parameter = invocation.getArgs()[1];
            if (parameter instanceof BaseEntity) {
                // 获取当前租户ID
                String shopId = TenantContext.getShopId();
                // 获取实体对象
                BaseEntity entity = (BaseEntity) parameter;
                // 如果ID为空，自动生成ID
                if (getEntityId(entity) == null) {
                    String tableName = getTableName(parameter.getClass());
                    if (!isIgnoreTable(tableName)) {
                        String idColumnName = convertToUnderline(getIdFieldName(entity.getClass()));
                        setEntityId(entity, idGenerator.nextId(tableName, shopId, idColumnName));
                    }
                }
            }
            //System.out.println("拦截到一个 INSERT 语句！");
        }
//        else {
//            System.out.println("拦截到一个非 INSERT 语句，SQL 类型为: " + sqlCommandType);
//        }


        return invocation.proceed();

    }

    private boolean isIgnoreTable(String tableName) {
        //IGNORE_TABLES
        for (String ignoreTable : IGNORE_TABLES) {
            if (StringUtils.equals(tableName, ignoreTable)) {
                return true;
            }
        }
        return false;
    }

    private Long getEntityId(Object entity) {
        // 通过反射获取ID字段值
        try {
            Field field = entity.getClass().getDeclaredField(getIdFieldName(entity.getClass()));
            field.setAccessible(true);
            return (Long) field.get(entity);
        } catch (Exception e) {
            return null;
        }
    }

    private void setEntityId(Object entity, Long id) {
        try {
            Field field = entity.getClass().getDeclaredField(getIdFieldName(entity.getClass()));
            field.setAccessible(true);
            field.set(entity, id);
        } catch (Exception e) {
            // 处理异常
        }
    }

    private String getIdFieldName(Class<?> entityClass) {
        // 遍历所有字段查找带有 @TableId 注解的字段
        for (Field field : entityClass.getDeclaredFields()) {
            TableId tableId = field.getAnnotation(TableId.class);
            if (tableId != null) {
                return !tableId.value().isEmpty() ? tableId.value() : field.getName();
            }
        }
        return "id";
    }

    private String getTableName(Class<?> entityClass) {
        // 查找类上的 @TableName 注解
        TableName tableName = entityClass.getAnnotation(TableName.class);
        if (tableName != null && !tableName.value().isEmpty()) {
            return tableName.value();
        }
        // 如果没有注解，将类名转换为下划线格式
        String className = entityClass.getSimpleName();
        return convertToUnderline(className);
    }

    private String convertToUnderline(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }
}
