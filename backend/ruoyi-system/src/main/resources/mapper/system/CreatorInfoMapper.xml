<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CreatorInfoMapper">
    
    <resultMap type="CreatorInfo" id="CreatorInfoResult">
        <id     property="id"               column="id"               />
        <result property="creatorId"        column="creator_id"       />
        <result property="avatarUrl"        column="avatar_url"       />
        <result property="password"         column="password"         />
        <result property="openid"           column="openid"           />
        <result property="unionid"          column="unionid"          />
        <result property="wxAccount"        column="wx_account"       />
        <result property="phoneNumber"      column="phone_number"     />
        <result property="purePhoneNumber"  column="pure_phone_number"/>
        <result property="countryCode"      column="country_code"     />
        <result property="wxMaAppid"        column="wx_ma_appid"      />
        <result property="createTime"       column="create_time"      />
        <result property="updateTime"       column="update_time"      />
    </resultMap>
    
    <sql id="selectCreatorInfoVo">
        select id, creator_id, avatar_url, password, openid, unionid, wx_account, 
               phone_number, pure_phone_number, country_code, wx_ma_appid, 
               create_time, update_time
        from creator_info
    </sql>
    
    <select id="selectCreatorInfoList" parameterType="CreatorInfo" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        <where>
            <if test="creatorId != null and creatorId != ''">
                AND creator_id = #{creatorId}
            </if>
            <if test="openid != null and openid != ''">
                AND openid = #{openid}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">
                AND pure_phone_number = #{purePhoneNumber}
            </if>
            <if test="countryCode != null and countryCode != ''">
                AND country_code = #{countryCode}
            </if>
            <if test="wxMaAppid != null and wxMaAppid != ''">
                AND wx_ma_appid = #{wxMaAppid}
            </if>
        </where>
    </select>
    
    <select id="selectCreatorInfoById" parameterType="Long" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where id = #{id}
    </select>
    
    <select id="selectCreatorInfoByCreatorId" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where creator_id = #{creatorId}
    </select>
    
    <select id="selectCreatorInfoByPhoneNumber" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where phone_number = #{phoneNumber}
    </select>
    
    <select id="selectCreatorInfoByOpenid" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where openid = #{openid}
    </select>
    
    <select id="selectCreatorInfoByOpenidAndAppid" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where openid = #{openid} and wx_ma_appid = #{wxMaAppid}
    </select>
    
    <select id="checkCreatorIdUnique" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where creator_id = #{creatorId} limit 1
    </select>
    
    <select id="checkPhoneNumberUnique" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where phone_number = #{phoneNumber} limit 1
    </select>
    
    <select id="checkOpenidUnique" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where openid = #{openid} limit 1
    </select>
    <select id="selectCreatorInfoByUnionid" parameterType="String" resultMap="CreatorInfoResult">
        <include refid="selectCreatorInfoVo"/>
        where unionid = #{unionid} limit 1
    </select>

    <insert id="insertCreatorInfo" parameterType="CreatorInfo" useGeneratedKeys="true" keyProperty="id">
        insert into creator_info(
            <if test="creatorId != null and creatorId != ''">creator_id,</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null and unionid != ''">unionid,</if>
            <if test="wxAccount != null and wxAccount != ''">wx_account,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">pure_phone_number,</if>
            <if test="countryCode != null and countryCode != ''">country_code,</if>
            <if test="wxMaAppid != null and wxMaAppid != ''">wx_ma_appid,</if>
            create_time
        )values(
            <if test="creatorId != null and creatorId != ''">#{creatorId},</if>
            <if test="avatarUrl != null and avatarUrl != ''">#{avatarUrl},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null and unionid != ''">#{unionid},</if>
            <if test="wxAccount != null and wxAccount != ''">#{wxAccount},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">#{purePhoneNumber},</if>
            <if test="countryCode != null and countryCode != ''">#{countryCode},</if>
            <if test="wxMaAppid != null and wxMaAppid != ''">#{wxMaAppid},</if>
            sysdate()
        )
    </insert>

    <update id="updateCreatorInfo" parameterType="CreatorInfo">
        update creator_info
        <set>
            <if test="creatorId != null and creatorId != ''">creator_id = #{creatorId},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="wxAccount != null">wx_account = #{wxAccount},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="purePhoneNumber != null">pure_phone_number = #{purePhoneNumber},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="wxMaAppid != null">wx_ma_appid = #{wxMaAppid},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteCreatorInfoById" parameterType="Long">
        delete from creator_info where id = #{id}
    </delete>

    <delete id="deleteCreatorInfoByIds" parameterType="String">
        delete from creator_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
