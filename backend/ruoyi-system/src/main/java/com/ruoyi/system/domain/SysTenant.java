package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 租户对象 sys_tenant
 */
public class SysTenant extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @TableId
    @Excel(name = "租户序号")
    private String shopId;

    /** 租户名称 */
    @Excel(name = "租户名称")
    private String tenantName;

    /** 租户编码 */
    @Excel(name = "租户编码")
    private String tenantCode;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /** 问到用户id */
    @Excel(name = "问到用户id")
    private String bUserId;

    /** 试用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "试用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date trialStartTime;

    /** 试用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "试用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date trialEndTime;

    /** 试用状态：0-未试用，1-试用中，2-试用过期，3-已转正 */
    @Excel(name = "试用状态", readConverterExp = "0=未试用,1=试用中,2=试用过期,3=已转正")
    private Integer trialStatus;

    /** 试用天数 */
    @Excel(name = "试用天数")
    private Integer trialDays;

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    @NotBlank(message = "租户名称不能为空")
    @Size(min = 0, max = 50, message = "租户名称长度不能超过50个字符")
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    @NotBlank(message = "租户编码不能为空")
    @Size(min = 0, max = 64, message = "租户编码长度不能超过64个字符")
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getbUserId() {
        return bUserId;
    }

    public void setbUserId(String bUserId) {
        this.bUserId = bUserId;
    }

    public Date getTrialStartTime() {
        return trialStartTime;
    }

    public void setTrialStartTime(Date trialStartTime) {
        this.trialStartTime = trialStartTime;
    }

    public Date getTrialEndTime() {
        return trialEndTime;
    }

    public void setTrialEndTime(Date trialEndTime) {
        this.trialEndTime = trialEndTime;
    }

    public Integer getTrialStatus() {
        return trialStatus;
    }

    public void setTrialStatus(Integer trialStatus) {
        this.trialStatus = trialStatus;
    }

    public Integer getTrialDays() {
        return trialDays;
    }

    public void setTrialDays(Integer trialDays) {
        this.trialDays = trialDays;
    }

    /**
     * 判断是否在试用期内
     */
    public boolean isInTrial() {
        return trialStatus != null && trialStatus == 1 && 
               trialEndTime != null && trialEndTime.after(new Date());
    }

    /**
     * 判断试用是否已过期
     */
    public boolean isTrialExpired() {
        return trialStatus != null && (trialStatus == 2 || 
               (trialStatus == 1 && trialEndTime != null && trialEndTime.before(new Date())));
    }

    /**
     * 判断是否已转正
     */
    public boolean isConverted() {
        return trialStatus != null && trialStatus == 3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("shopId", getShopId())
                .append("tenantName", getTenantName())
                .append("tenantCode", getTenantCode())
                .append("status", getStatus())
                .append("trialStartTime", getTrialStartTime())
                .append("trialEndTime", getTrialEndTime())
                .append("trialStatus", getTrialStatus())
                .append("trialDays", getTrialDays())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("bUserId", getbUserId())
                .toString();
    }
} 