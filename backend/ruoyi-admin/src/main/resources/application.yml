# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 'vZ8KTmP9nX6po5wR7jH4cF9LA3gE9yB1uu8sN8hM4kW2pQ5tY7xC3bD1pl4'
  # 令牌有效期（默认1440分钟,一天）
  expireTime: 1440

# token配置
creatorToken:
  # 令牌自定义标识
  header: CreatorAuthToken
  # 令牌密钥
  secret: 'KZ8KTmP9nX6po5wR7jH4cF9LA3gE9yB1uu8sN8hM4kW2pQ5tY7xC3bD9DL7'
  # 令牌有效期（默认1440分钟,一天）
  expireTime: 1440

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
wx:
  miniapp:
    appid: wx0e5e01d239197bb1
    secret: 76bed2dbbab0dc3c30302736ac383426
    token: asdfadfad
    aesKey: MGGMcNq1VVLjB0ArAisupCPDqP6xY64y1DjPDDEqJa5
    msgDataFormat: JSON
tencent:
  captcha:
    captcha-app-id: 192824652 # 从环境变量获取，需要在腾讯云验证码控制台获取
    app-secret-key: DcFo2A7miP6veg4jhHwkOwVaC # 从环境变量获取，需要在腾讯云验证码控制台获取
    secret-id: AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps # 从环境变量获取，腾讯云API密钥
    secret-key: 73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9 # 从环境变量获取，腾讯云API密钥

yunpian:
  sms:
    apikey: 102222afce402a62dd7b44276e736fff # 从环境变量获取云片apikey
    tpl-id: 6056480 # 从环境变量获取短信模板ID

aliyun:
  sms:
    access-key-id: LTAI5tRfCNfcpvFCRaM1ydiy # 从环境变量获取阿里云AccessKeyId
    access-key-secret: ****************************** # 从环境变量获取阿里云AccessKeySecret
    sign-name: 问到 # 从环境变量获取短信签名
    template-code: SMS_244365161 # 从环境变量获取短信模板ID
    region-id: cn-hangzhou # 地域ID
    endpoint: dysmsapi.aliyuncs.com # 接入点
