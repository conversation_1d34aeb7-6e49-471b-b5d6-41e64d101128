package com.ruoyi.web.controller.system;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.web.jwt.CreatorJwtUtil;
import com.ruoyi.system.domain.CreatorInfo;
import com.ruoyi.system.domain.WxLogin;
import com.ruoyi.system.service.ICreatorInfoService;
import com.ruoyi.system.service.IWxLoginService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 创作者后台登录逻辑
 */
@Slf4j
@RestController
@RequestMapping("/creator")
public class CreatorController extends BaseController {
    private static final int DEFAULT_WIDTH = 430;
    @Autowired
    private IWxLoginService wxLoginService;
    @Autowired
    private ICreatorInfoService creatorInfoService;
    @Autowired
    private CreatorJwtUtil creatorJwtUtil;
    @Autowired
    private WxMaService wxMaService;

    /**
     * 1秒请求一次,前端轮询此接口来判断扫码登录是否完成
     * wxLogin.getScanSuccess()==3为登录成功
     * wxLogin.getIsExpired()==1则为二维码过期,需要页面刷新新的二维码
     *
     * @param uuid
     * @return
     */
    @Anonymous
    @GetMapping("/login_status")
    public AjaxResult loginStatus(@RequestParam("uuid") String uuid) {
        WxLogin wxLogin = wxLoginService.checkLoginStatus(uuid);
        if (wxLogin != null) {
            Date now = new Date();
            if (wxLogin.getIsExpired() != 1 && now.after(wxLogin.getExpirationTime())) {
                wxLogin.setIsExpired(1);
                wxLoginService.updateWxLogin(wxLogin);
            }
            return success(wxLogin);
        } else {
            return error("登录未完成或二维码已过期");
        }
    }

    /**
     * 登录成功后,创建创作者的登录token
     * 是上一个接口wxLogin.getScanSuccess()==3时然后调用此接口
     *
     * @param uuid
     * @param creatorId
     * @return
     */
    @Anonymous
    @GetMapping("/getCreatorToken")
    public AjaxResult getCreatorToken(@RequestParam("uuid") String uuid, @RequestParam("creatorId") String creatorId) {
        WxLogin wxLogin = wxLoginService.checkLoginStatus(uuid);
        if (wxLogin == null) {
            return error("登录未完成或二维码已过期");
        }
        if (wxLogin.getScanSuccess() != 3) {
            return error("登录未完成或二维码已过期");
        }
        if (!creatorId.equals(wxLogin.getCreatorId())) {
            return error("用户登录信息不匹配");
        }
        //查询creatorInfo
        CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByCreatorId(creatorId);
        if (creatorInfo == null) {
            return error("用户不存在");
        }
        //这个token是有过期时间的,当需要检验时先判断是否过期.
        String token = creatorJwtUtil.generateToken(creatorId);
        Map<String, Object> result = new HashMap<>();
        result.put("creatorToken", token);
        result.put("creatorInfo", creatorInfo);
        return success("登录成功", result);
    }

    /**
     * 创建小程序扫码二维码并返回Base64编码和uuid
     * uuid以scene参数传入小程序页面,pages/creatorLogin/creatorLogin
     */
    @Anonymous
    @GetMapping("/createQrcode")
    public AjaxResult createQrcode() {
        String scene = UUID.randomUUID().toString().replace("-", "");
        String page = "pages/creatorLogin/creatorLogin";
        WxMaCodeLineColor lineColor = new WxMaCodeLineColor();
        try {
            //这里先设置为开发版,并不校验路径
            byte[] releases = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(scene, page, false, "develop", DEFAULT_WIDTH, false, lineColor, false);
            String base64String = Base64.getEncoder().encodeToString(releases);
            GetPcLoginResultDTO result = GetPcLoginResultDTO.builder()
                    .base64(base64String)
                    .uuid(scene)
                    .build();
            WxLogin wxLogin = new WxLogin();
            wxLogin.setUuid(scene);
            //当前时间加5分钟
            wxLogin.setExpirationTime(DateUtils.addMinutes(new Date(), 5));
            wxLoginService.insertWxLogin(wxLogin);
            return AjaxResult.success("二维码生成成功", result);
        } catch (WxErrorException e) {
            return error(e.getError().getErrorMsg());
        }
    }

    // DTO定义
    @Data
    @Builder
    public static class GetPcLoginResultDTO {
        private String uuid;
        private String base64;
    }
}